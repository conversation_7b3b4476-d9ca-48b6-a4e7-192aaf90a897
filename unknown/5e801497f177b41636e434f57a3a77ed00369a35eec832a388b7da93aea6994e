'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, Users, FileText, CheckCircle, ImageIcon, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { fetchMonthlyMeetings, type MonthlyMeeting } from '@/lib/strapi'
import AnimatedHero from '@/components/animated-hero'
import ImageSlider from '@/components/image-slider'

export default function MonthlyMeetingsPage() {
  const [meetings, setMeetings] = useState<MonthlyMeeting[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching monthly meetings...')
        const meetingsData = await fetchMonthlyMeetings({ populate: '*' })
        console.log('Fetched monthly meetings:', meetingsData)

        if (meetingsData && meetingsData.length > 0) {
          setMeetings(meetingsData) // Get all items
        } else {
          setError('No monthly meetings data found')
        }
      } catch (err) {
        console.error('Error fetching monthly meetings:', err)
        setError(err instanceof Error ? err.message : 'An error occurred while fetching data')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: string | undefined) => {
    return content && content.length > 100;
  };

  // Helper function to render content with expand/collapse
  const renderContent = (content: string | undefined, isExpanded: boolean) => {
    if (!content) return null;

    return (
      <p className={`text-sm text-muted-foreground ${isExpanded ? '' : 'line-clamp-2'}`}>
        {content}
      </p>
    );
  };

  if (error) {
    return (
      <div className="min-h-screen">
        {/* Hero Section */}
        <AnimatedHero
          title={{
            zh: "月會",
            en: "Monthly Meetings",
          }}
          description={{
            zh: "了解九龍總商會每月例會的議程、決議和重要討論事項",
            en: "Learn about KCC monthly meeting agendas, decisions, and important discussions",
          }}
          image="/placeholder.svg"
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              <span className="block">發生錯誤</span>
              <span className="text-base text-red-600">An error occurred</span>
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={{
          zh: "月會",
          en: "Monthly Meetings",
        }}
        description={{
          zh: "了解九龍總商會每月例會的議程、決議和重要討論事項",
          en: "Learn about KCC monthly meeting agendas, decisions, and important discussions",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        {/* Introduction Card */}
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">月會 / Monthly Meetings</h2>
              <p>
                九龍總商會每月例會記錄重要議程、特邀嘉賓和會議決議，確保組織運作的透明度和效率。
                我們定期舉行月會以討論商會發展方向、審議重要提案，並促進會員間的交流與合作。
              </p>
              <p>
                KCC monthly meetings document important agendas, special guests, and meeting decisions, ensuring organizational transparency and efficiency.
                We regularly hold monthly meetings to discuss the chamber's development direction, review important proposals, and promote communication and cooperation among members.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Meetings Listing */}
        <section>
          {loading ? (
            <div className="grid md:grid-cols-2 gap-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* Monthly Meetings Items */}
              {meetings.length > 0 ? (
                <div className="space-y-6">
                  <div className="border-b pb-2">
                    <h2 className="text-2xl font-bold">
                      <span className="text-primary">月會記錄</span> / Monthly Meeting Records
                    </h2>
                    <p className="text-muted-foreground">九龍總商會每月例會記錄 / KCC monthly meeting records</p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {meetings.map((meeting) => {
                      const isExpanded = expandedCards[meeting.id]
                      const hasLongContent = shouldShowReadMore(meeting.agenda) ||
                                           shouldShowReadMore(meeting.special_guests) ||
                                           shouldShowReadMore(meeting.decisions_taken)

                      return (
                        <Card key={meeting.id} className="overflow-hidden hover:shadow-lg transition-all">
                          {/* Image Slider for multiple images */}
                          {meeting.image && meeting.image.length > 0 ? (
                            <ImageSlider
                              images={meeting.image.map(img => ({
                                id: img.id,
                                url: img.url,
                                formats: img.formats
                              }))}
                              alt="Monthly Meeting Image"
                              interval={3000}
                            />
                          ) : (
                            <div className="relative aspect-video">
                              <Image
                                src="/placeholder.svg"
                                alt="Monthly Meeting"
                                fill
                                className="object-cover"
                              />
                            </div>
                          )}
                          <CardContent className="p-6">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{new Date(meeting.dateofmeeting).toLocaleDateString()}</time>
                            </div>
                            <h3 className="text-xl font-bold mb-2">月會 / Monthly Meeting</h3>

                            {/* Agenda */}
                            <div className="space-y-3 mb-4">
                              <div>
                                <h4 className="font-semibold text-sm flex items-center gap-2 mb-1">
                                  <FileText className="h-4 w-4" />
                                  議程 / Agenda
                                </h4>
                                {renderContent(meeting.agenda, isExpanded)}
                              </div>

                              {meeting.special_guests && (
                                <div>
                                  <h4 className="font-semibold text-sm flex items-center gap-2 mb-1">
                                    <Users className="h-4 w-4" />
                                    特邀嘉賓 / Special Guests
                                  </h4>
                                  {renderContent(meeting.special_guests, isExpanded)}
                                </div>
                              )}

                              {meeting.decisions_taken && (
                                <div>
                                  <h4 className="font-semibold text-sm flex items-center gap-2 mb-1">
                                    <CheckCircle className="h-4 w-4" />
                                    會議決議 / Decisions
                                  </h4>
                                  {renderContent(meeting.decisions_taken, isExpanded)}
                                </div>
                              )}
                            </div>

                            {hasLongContent && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleExpanded(meeting.id)}
                                className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                              >
                                {isExpanded ? (
                                  <>
                                    Show Less <ChevronUp className="ml-1 h-4 w-4" />
                                  </>
                                ) : (
                                  <>
                                    Read More <ChevronDown className="ml-1 h-4 w-4" />
                                  </>
                                )}
                              </Button>
                            )}
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    <span className="block">暫無月會記錄</span>
                    <span className="block">No monthly meeting records available at the moment</span>
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}
