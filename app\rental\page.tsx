"use client"

import { useState, useEffect, Suspense, useMemo } from "react"
import { useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronUp } from "lucide-react"
import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import ImageSlider from "@/components/image-slider"
import { fetchRentals } from "@/strapi"
import { Skeleton } from "@/components/ui/skeleton"
import { MapPin, Phone, Mail, Home } from "lucide-react"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImage {
  id: number
  url: string
  alternativeText?: string
  caption?: string
  width?: number
  height?: number
  formats?: {
    large?: StrapiImageFormat
    medium?: StrapiImageFormat
    small?: StrapiImageFormat
    thumbnail?: StrapiImageFormat
  }
}

interface Rental {
  id: number
  documentId: string
  Title: string
  Subtitle: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }>
  location: string
  email: string
  number: string
  area: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image?: StrapiImage | StrapiImage[]
  images?: StrapiImage[]
}

interface StrapiResponse {
  data: Rental[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

// Loading component
function RentalLoading() {
  return (
    <div className="container py-12">
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3, 4, 5, 6].map((i) => (
          <Card key={i} className="overflow-hidden">
            <div className="aspect-video relative">
              <Skeleton className="absolute inset-0" />
            </div>
            <CardContent className="p-6">
              <Skeleton className="h-6 w-3/4 mb-4" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-2/3 mb-4" />
              <div className="space-y-2">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

// Main content component
function RentalContent() {
  const searchParams = useSearchParams()
  const [rentals, setRentals] = useState<Rental[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set())

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  const toggleExpanded = (itemId: number) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }

  const loadRentals = async (page = 1) => {
    try {
      setLoading(true)

      console.log('Rental page - Current language:', currentLang)
      console.log('Rental page - Strapi locale:', getStrapiLocale)

      const response = await fetchRentals({
        populate: "*",
        pagination: { page, pageSize: 6 },
        locale: getStrapiLocale
      }) as StrapiResponse

      if (page === 1) {
        setRentals(response.data)
        setCurrentPage(1)
      } else {
        setRentals(prev => [...prev, ...response.data])
        setCurrentPage(page)
      }

      setHasMore(response.meta.pagination.page < response.meta.pagination.pageCount)
      setError(null)
    } catch (err: any) {
      console.error("Error fetching rentals:", err)
      setError(t('rental.error'))
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadRentals(1)
  }, [currentLang, getStrapiLocale]) // Re-run when language changes

  // Helper function to render structured content with read more functionality
  const renderStructuredContent = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | undefined, itemId: number, maxLength: number = 150) => {
    if (!content || !Array.isArray(content)) {
      return <p className="text-muted-foreground">{t('rental.noContent')}</p>
    }

    // Filter out empty content
    const validContent = content.filter(block => {
      if (block.type === "paragraph") {
        const text = block.children?.map(child => child.text).join('').trim()
        return text && text.length > 0
      }
      return false
    })

    if (validContent.length === 0) {
      return <p className="text-muted-foreground">{t('rental.noContent')}</p>
    }

    const fullText = validContent.map(block =>
      block.children?.map(child => child.text).join('')
    ).join(' ')

    const isExpanded = expandedItems.has(itemId)
    const shouldTruncate = fullText.length > maxLength
    const displayText = shouldTruncate && !isExpanded
      ? fullText.substring(0, maxLength) + '...'
      : fullText

    return (
      <div>
        <p className="mb-2">{displayText}</p>
        {shouldTruncate && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => toggleExpanded(itemId)}
            className="text-primary hover:text-primary/80 p-0 h-auto"
          >
            {isExpanded ? (
              <>
                {t('rental.readLess')} <ChevronUp className="ml-1 h-3 w-3" />
              </>
            ) : (
              <>
                {t('rental.readMore')} <ChevronDown className="ml-1 h-3 w-3" />
              </>
            )}
          </Button>
        )}
      </div>
    )
  }

  // Helper function to get images for ImageSlider
  const getRentalImages = (rental: Rental): StrapiImage[] => {
    const images: StrapiImage[] = []

    // Check if image is an array (multiple images)
    if (Array.isArray(rental.image)) {
      images.push(...rental.image)
    } else if (rental.image) {
      // Single image
      images.push(rental.image)
    }

    // Also check images field
    if (rental.images && Array.isArray(rental.images)) {
      images.push(...rental.images)
    }

    return images
  }

  // Helper function to get optimal image URL
  const getOptimalImageUrl = (image: StrapiImage): string => {
    if (image.formats?.medium?.url) {
      return image.formats.medium.url
    }
    if (image.formats?.small?.url) {
      return image.formats.small.url
    }
    if (image.formats?.large?.url) {
      return image.formats.large.url
    }
    if (image.url) {
      return image.url
    }
    return "/placeholder.svg"
  }

  return (
    <div className="container py-12">
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="prose prose-lg max-w-none text-center">
            <h2 className="text-center">{t('rental.staticTitle')}</h2>
            <p>{t('rental.staticDescription')}</p>
          </div>
        </CardContent>
      </Card>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-8">
          {error}
        </div>
      )}

      {loading && rentals.length === 0 ? (
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="overflow-hidden">
              <div className="aspect-video relative">
                <Skeleton className="absolute inset-0" />
              </div>
              <CardContent className="p-6">
                <Skeleton className="h-6 w-3/4 mb-4" />
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-2/3 mb-4" />
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : rentals.length > 0 ? (
        <>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {rentals.map((rental) => {
              const images = getRentalImages(rental)

              return (
                <Card key={rental.id} className="overflow-hidden">
                  <div className="relative aspect-video">
                    {images.length > 0 ? (
                      images.length > 1 ? (
                        <ImageSlider
                          images={images}
                          alt={rental.Title || "Rental Property"}
                          interval={3000}
                        />
                      ) : (
                        <Image
                          src={getOptimalImageUrl(images[0])}
                          alt={`${rental.Title} - Rental Property`}
                          fill
                          className="object-cover"
                          onError={(e) => {
                            console.error("Error loading rental image");
                            (e.target as HTMLImageElement).src = "/placeholder.svg";
                          }}
                        />
                      )
                    ) : (
                      <div className="flex items-center justify-center h-full bg-gray-100">
                        <p className="text-muted-foreground p-4 text-center">{t('rental.noContent')}</p>
                      </div>
                    )}
                  </div>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-bold mb-2">{rental.Title}</h3>
                    <div className="text-muted-foreground mb-4">
                      {renderStructuredContent(rental.Subtitle, rental.id)}
                    </div>

                    <div className="space-y-2 text-sm text-muted-foreground">
                      {rental.location && (
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 flex-shrink-0" />
                          <span>{rental.location}</span>
                        </div>
                      )}
                      {rental.area && (
                        <div className="flex items-center gap-2">
                          <Home className="h-4 w-4 flex-shrink-0" />
                          <span>{rental.area}</span>
                        </div>
                      )}
                      {rental.number && (
                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 flex-shrink-0" />
                          <span>{rental.number}</span>
                        </div>
                      )}
                      {rental.email && (
                        <div className="flex items-center gap-2">
                          <Mail className="h-4 w-4 flex-shrink-0" />
                          <span>{rental.email}</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {hasMore && (
            <div className="flex justify-center mt-8">
              <Button
                onClick={() => loadRentals(currentPage + 1)}
                disabled={loading}
                className="px-6 py-2"
              >
                {loading ? t('rental.loading') : t('rental.loadMore')}
              </Button>
            </div>
          )}
        </>
      ) : (
        <Card>
          <CardContent className="p-8">
            <div className="text-center py-12">
              <p className="text-muted-foreground">{t('rental.noData')}</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

// Component that uses searchParams
function RentalPage() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="rental.title"
        description="rental.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <Suspense fallback={<RentalLoading />}>
        <RentalContent />
      </Suspense>
    </div>
  )
}

// Main export with Suspense wrapper
export default function Rental() {
  return (
    <Suspense fallback={<RentalLoading />}>
      <RentalPage />
    </Suspense>
  )
}