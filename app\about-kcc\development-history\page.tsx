'use client'

import { useState, useEffect, useMemo, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { fetchKCCDevelopmentHistories, type KCCDevelopmentHistory } from '@/lib/strapi'
import AnimatedHero from '@/components/animated-hero'
import ImageSlider from '@/components/image-slider'
import { useTranslation, getLanguageFromParams } from '@/lib/translations'

// Loading component
function DevelopmentHistoryLoading() {
  return (
    <div className="container py-12">
      <div className="grid md:grid-cols-2 gap-6">
        {Array(4).fill(null).map((_, index) => (
          <Card key={`skeleton-${index}`} className="overflow-hidden">
            <div className="relative aspect-video">
              <Skeleton className="absolute inset-0" />
            </div>
            <CardContent className="p-6 space-y-4">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-16 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

// Main content component
function DevelopmentHistoryContent() {
  const searchParams = useSearchParams()
  const [histories, setHistories] = useState<KCCDevelopmentHistory[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: string | undefined) => {
    return content && content.length > 100;
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        console.log('Development History - Current language:', currentLang)
        console.log('Development History - Strapi locale:', getStrapiLocale)

        // Use the original working API call with locale parameter
        const historiesData = await fetchKCCDevelopmentHistories({
          populate: '*',
          locale: getStrapiLocale
        })

        console.log('Development History - API response:', historiesData)

        if (historiesData && historiesData.length > 0) {
          setHistories(historiesData)
        } else {
          // If no data found with locale, try without locale as fallback
          console.log('No data found with locale, trying without locale...')
          try {
            const fallbackData = await fetchKCCDevelopmentHistories({ populate: '*' })
            console.log('Development History - Fallback API Response:', fallbackData)

            if (fallbackData && fallbackData.length > 0) {
              setHistories(fallbackData)
            } else {
              console.error('Development history data not found in fallback response:', fallbackData)
              setError(t('developmentHistory.noHistory'))
            }
          } catch (fallbackErr) {
            console.error('Fallback request error:', fallbackErr)
            setError(t('developmentHistory.noHistory'))
          }
        }
      } catch (err: any) {
        console.error('Error fetching development histories:', err)
        setError(t('developmentHistory.error'))
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [currentLang, getStrapiLocale]) // Re-run when language changes

  if (error) {
    return (
      <div className="container py-12">
        <Card>
          <CardContent className="p-8">
            <div className="text-center py-12">
              <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
                <h2 className="text-xl font-semibold text-red-700 mb-2">
                  {t('common.error')}
                </h2>
                <p className="text-red-600">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-12">
      {/* Introduction Card */}
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="prose prose-lg max-w-none text-center">
            <h2 className="text-center">{t('developmentHistory.staticTitle')}</h2>
            <p>{t('developmentHistory.staticDescription')}</p>
          </div>
        </CardContent>
      </Card>

      {/* Development History Listing */}
      <section>
        {loading ? (
          <div className="grid md:grid-cols-2 gap-6">
            {Array(4).fill(null).map((_, index) => (
              <Card key={`skeleton-${index}`} className="overflow-hidden">
                <div className="relative aspect-video">
                  <Skeleton className="absolute inset-0" />
                </div>
                <CardContent className="p-6 space-y-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <>
            {histories.length > 0 ? (
              <div className="space-y-6">
                <div className="border-b pb-2">
                  <h2 className="text-2xl font-bold">
                    <span className="text-primary">{t('developmentHistory.milestonesTitle')}</span>
                  </h2>
                  <p className="text-muted-foreground">{t('developmentHistory.milestonesDescription')}</p>
                </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {histories.map((history) => {
                      const isExpanded = expandedCards[history.id]
                      const showReadMore = shouldShowReadMore(history.description)

                      return (
                        <Card key={history.id} className="overflow-hidden hover:shadow-lg transition-all">
                          {/* Image Slider for multiple images */}
                          {history.image && history.image.length > 0 ? (
                            <ImageSlider
                              images={history.image.map(img => ({
                                id: img.id,
                                url: img.url,
                                formats: img.formats
                              }))}
                              alt={history.title || "Development History Image"}
                              interval={3000}
                            />
                          ) : (
                            <div className="relative aspect-video">
                              <Image
                                src="/placeholder.svg"
                                alt={history.title || "Development History"}
                                fill
                                className="object-cover"
                              />
                            </div>
                          )}
                          <CardContent className="p-6">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{history.year}</time>
                            </div>
                            <h3 className="text-xl font-bold mb-2">{history.title}</h3>
                            <div className="space-y-2 mb-4">
                              {history.location && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <MapPin className="h-4 w-4" />
                                  <span>{history.location}</span>
                                </div>
                              )}
                              {history.participants && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Users className="h-4 w-4" />
                                  <span>{history.participants}</span>
                                </div>
                              )}
                            </div>
                            <div className={`text-muted-foreground ${isExpanded ? '' : 'line-clamp-3'}`}>
                              {history.description}
                            </div>

                            {showReadMore && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleExpanded(history.id)}
                                className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                              >
                                {isExpanded ? (
                                  <>
                                    {t('developmentHistory.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
                                  </>
                                ) : (
                                  <>
                                    {t('developmentHistory.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                                  </>
                                )}
                              </Button>
                            )}
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
              </div>
            ) : (
              <Card>
                <CardContent className="p-8">
                  <div className="text-center py-12">
                    <p className="text-muted-foreground">{t('developmentHistory.noHistory')}</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </>
        )}
      </section>
    </div>
  )
}

// Component that uses searchParams - wrapped in Suspense
function DevelopmentHistoryPageContent() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="developmentHistory.title"
        description="developmentHistory.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <Suspense fallback={<DevelopmentHistoryLoading />}>
        <DevelopmentHistoryContent />
      </Suspense>
    </div>
  )
}

// Main page component that wraps everything in Suspense
export default function DevelopmentHistoryPage() {
  return (
    <Suspense fallback={<DevelopmentHistoryLoading />}>
      <DevelopmentHistoryPageContent />
    </Suspense>
  )
}
