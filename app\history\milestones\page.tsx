"use client"

import { useState, useEffect, Suspense, useMemo } from "react"
import { useSearchParams } from "next/navigation"

import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronUp } from "lucide-react"
import Image from "next/image"

import AnimatedHero from "@/components/animated-hero"
import ImageSlider from "@/components/image-slider"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { fetchMilestones } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId: string
  name: string
  alternativeText?: string
  caption?: string
  width: number
  height: number
  formats?: StrapiImageFormats
  hash: string
  ext: string
  mime: string
  size: number
  url: string
  previewUrl?: string
  provider: string
  provider_metadata?: any
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale?: string
}

interface Milestone {
  id: number
  documentId: string
  title: string
  titleEn?: string
  date?: string
  description: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }>
  order?: number
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  media?: StrapiImage | StrapiImage[] | null
  image?: StrapiImage | StrapiImage[] | null
  images?: StrapiImage[]
  seo?: any
  localizations?: any[]
}

// Loading component
function MilestonesLoading() {
  return (
    <div className="container py-12">
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4 mx-auto" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        </CardContent>
      </Card>

      <div className="space-y-12">
        {Array(3).fill(0).map((_, index) => (
          <div key={index} className="relative">
            <div className="grid md:grid-cols-2 gap-8 items-center">
              <Skeleton className="h-64 w-full" />
              <div className="space-y-4">
                <Skeleton className="h-8 w-24" />
                <Skeleton className="h-10 w-3/4" />
                <Skeleton className="h-20 w-full" />
                <Skeleton className="h-20 w-full" />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// Main content component
function MilestonesContent() {
  const searchParams = useSearchParams()
  const [milestones, setMilestones] = useState<Milestone[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedMilestones, setExpandedMilestones] = useState<Set<number>>(new Set())

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  const toggleExpanded = (milestoneId: number) => {
    setExpandedMilestones(prev => {
      const newSet = new Set(prev)
      if (newSet.has(milestoneId)) {
        newSet.delete(milestoneId)
      } else {
        newSet.add(milestoneId)
      }
      return newSet
    })
  }

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae'

        // Build URL with locale parameter
        const url = new URL(`${strapiUrl}/milestones`)
        url.searchParams.append('populate', '*')
        if (getStrapiLocale) {
          url.searchParams.append('locale', getStrapiLocale)
        }

        console.log('Milestones - API URL:', url.toString())

        const response = await fetch(url.toString(), {
          headers: {
            'Authorization': `Bearer ${strapiToken}`,
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        console.log('Milestones - API Response:', result)

        if (result && result.data) {
          const milestonesData = result.data as Milestone[]

          // Sort the milestones by date if available
          const sortedMilestones = [...milestonesData].sort((a, b) => {
            if (!a.date && !b.date) return 0
            if (!a.date) return 1
            if (!b.date) return -1
            return new Date(a.date).getTime() - new Date(b.date).getTime()
          })

          setMilestones(sortedMilestones)
        } else {
          console.error('Milestones data not found in response:', result)
          setError(t('milestones.noMilestones'))
        }
      } catch (error: any) {
        console.error('Error fetching milestones data:', error)
        setError(t('milestones.error'))
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [currentLang, getStrapiLocale]) // Re-run when language changes

  // Helper function to render rich text content
  const renderRichText = (content: any[]) => {
    if (!content || !Array.isArray(content)) {
      return <p className="text-muted-foreground">{t('milestones.noContent')}</p>
    }

    return content.map((block, index) => {
      if (block.type === "paragraph") {
        return (
          <p key={index} className="mb-4">
            {block.children?.map((child: any, childIndex: number) => (
              <span key={childIndex}>{child.text}</span>
            ))}
          </p>
        )
      }
      return null
    })
  }

  // Helper function to get milestone images
  const getMilestoneImages = (milestone: Milestone): StrapiImage[] => {
    const images: StrapiImage[] = []

    // Check for images array
    if (milestone.images && Array.isArray(milestone.images)) {
      images.push(...milestone.images)
    }

    // Check for single image
    if (milestone.image) {
      if (Array.isArray(milestone.image)) {
        images.push(...milestone.image)
      } else {
        images.push(milestone.image)
      }
    }

    // Check for media
    if (milestone.media) {
      if (Array.isArray(milestone.media)) {
        images.push(...milestone.media)
      } else {
        images.push(milestone.media)
      }
    }

    return images
  }

  // Helper function to get image URL with fallback
  const getImageUrl = (image: StrapiImage | undefined): string => {
    if (!image) return "/placeholder.svg"

    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com'

    // Try different format sizes
    if (image.formats?.medium?.url) {
      return image.formats.medium.url.startsWith('http')
        ? image.formats.medium.url
        : `${strapiUrl}${image.formats.medium.url}`
    }
    if (image.formats?.small?.url) {
      return image.formats.small.url.startsWith('http')
        ? image.formats.small.url
        : `${strapiUrl}${image.formats.small.url}`
    }
    if (image.url) {
      return image.url.startsWith('http')
        ? image.url
        : `${strapiUrl}${image.url}`
    }

    return "/placeholder.svg"
  }

  return (
    <div className="container py-12">
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="prose prose-lg max-w-none text-center">
            <h2 className="text-center">{t('milestones.staticTitle')}</h2>
            <p>{t('milestones.staticDescription')}</p>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-8">
        {loading ? (
          // Loading skeletons
          Array(3).fill(0).map((_, index) => (
            <Card key={index}>
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-start">
                  <div className="space-y-4">
                    <Skeleton className="h-8 w-24" />
                    <Skeleton className="h-10 w-3/4" />
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-20 w-full" />
                  </div>
                  <Skeleton className="h-64 w-full" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : error ? (
          <Card>
            <CardContent className="p-8">
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                <p>{error}</p>
              </div>
            </CardContent>
          </Card>
        ) : milestones.length > 0 ? (
          milestones.map((milestone) => {
            const isExpanded = expandedMilestones.has(milestone.id)
            const images = getMilestoneImages(milestone)

            // Determine if content is long enough to show read more/less
            const hasLongContent = (milestone.description && milestone.description.length > 2)

            // Extract year from date
            const year = milestone.date
              ? new Date(milestone.date).getFullYear().toString()
              : "N/A"

            return (
              <Card key={milestone.id} className="overflow-hidden">
                <CardContent className="p-8">
                  <div className="grid md:grid-cols-2 gap-8 items-start">
                    <div className="space-y-4">
                      <div className="inline-block bg-primary/10 text-primary px-4 py-1 rounded-full text-sm font-medium">
                        {year}
                      </div>
                      <h1 className="text-3xl font-bold mb-4">{milestone.title}</h1>

                      {/* Description */}
                      {milestone.description && (
                        <div className={`space-y-4 ${!isExpanded && hasLongContent ? 'line-clamp-3' : ''}`}>
                          {renderRichText(milestone.description)}
                        </div>
                      )}

                      {/* Read More/Less Button */}
                      {hasLongContent && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(milestone.id)}
                          className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                        >
                          {isExpanded ? (
                            <>
                              {t('milestones.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
                            </>
                          ) : (
                            <>
                              {t('milestones.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                            </>
                          )}
                        </Button>
                      )}
                    </div>

                    {/* Image Section */}
                    <div className="space-y-4">
                      {images.length > 1 ? (
                        <ImageSlider
                          images={images.map(img => ({
                            src: getImageUrl(img),
                            alt: img.alternativeText || milestone.title || 'Milestone image',
                            caption: img.caption
                          }))}
                          className="aspect-video"
                        />
                      ) : images.length === 1 ? (
                        <div className="relative aspect-video">
                          <Image
                            src={getImageUrl(images[0])}
                            alt={images[0].alternativeText || milestone.title || 'Milestone image'}
                            fill
                            className="object-cover rounded-lg"
                            sizes="(max-width: 768px) 100vw, 50vw"
                          />
                        </div>
                      ) : (
                        <div className="relative aspect-video">
                          <Image
                            src="/placeholder.svg"
                            alt="Placeholder"
                            fill
                            className="object-cover rounded-lg"
                            sizes="(max-width: 768px) 100vw, 50vw"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })
        ) : (
          <Card>
            <CardContent className="p-8">
              <div className="text-center py-12">
                <p className="text-muted-foreground">{t('milestones.noMilestones')}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

// Component that uses searchParams
function MilestonesPage() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="milestones.title"
        description="milestones.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <Suspense fallback={<MilestonesLoading />}>
        <MilestonesContent />
      </Suspense>
    </div>
  )
}

export default MilestonesPage