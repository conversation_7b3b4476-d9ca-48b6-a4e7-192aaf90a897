"use client"

import { useState, use<PERSON>emo, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Mail, Phone, MapPin, Clock, Fax } from "lucide-react"
import AnimatedHero from "@/components/animated-hero"
import { submitContactForm } from "@/lib/strapi"
import { toast } from "sonner"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

// Loading component
function ContactLoading() {
  return (
    <div className="container py-12">
      <div className="grid lg:grid-cols-2 gap-8">
        <Card>
          <CardContent className="p-6 space-y-4">
            {Array(6).fill(null).map((_, index) => (
              <div key={index} className="h-4 bg-gray-200 rounded animate-pulse"></div>
            ))}
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6 space-y-4">
            {Array(8).fill(null).map((_, index) => (
              <div key={index} className="h-10 bg-gray-200 rounded animate-pulse"></div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

interface FormData {
  Name: string;
  CompanyName: string;
  email: string;
  Phone: string;
  Theme: string;
  Message: string;
}

// Main content component
function ContactContent() {
  const searchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    Name: "",
    CompanyName: "",
    email: "",
    Phone: "",
    Theme: "",
    Message: ""
  })

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      await submitContactForm(formData)
      toast.success(t('contact.submitSuccess'))
      // Reset form
      setFormData({
        Name: "",
        CompanyName: "",
        email: "",
        Phone: "",
        Theme: "",
        Message: ""
      })
    } catch (error) {
      console.error("Error submitting form:", error)
      toast.error(t('contact.submitError'))
    } finally {
      setIsLoading(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { id, value } = e.target
    setFormData(prev => ({
      ...prev,
      [id]: value
    }))
  }

  return (
    <div className="container py-12">
      {/* Introduction Card */}
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="prose prose-lg max-w-none text-center">
            <h2 className="text-center">{t('contact.staticTitle')}</h2>
            <p>{t('contact.staticDescription')}</p>
          </div>
        </CardContent>
      </Card>

      <div className="grid lg:grid-cols-2 gap-12">
        {/* Contact Information */}
        <div>
          <h2 className="text-2xl font-bold mb-8">{t('contact.contactInfo')}</h2>

          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="p-3 rounded-full bg-[#1E1B4B]/10">
                    <Mail className="h-6 w-6 text-[#1E1B4B]" />
                  </div>
                  <div>
                    <h3 className="font-bold mb-1">{t('contact.email')}</h3>
                    <p className="text-gray-600"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="p-3 rounded-full bg-[#1E1B4B]/10">
                    <Phone className="h-6 w-6 text-[#1E1B4B]" />
                  </div>
                  <div>
                    <h3 className="font-bold mb-1">{t('contact.phone')} / {t('contact.fax')}</h3>
                    <p className="text-gray-600">{t('contact.phone')}：2760 0393</p>
                    <p className="text-gray-600">{t('contact.fax')}：2761 0166</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="p-3 rounded-full bg-[#1E1B4B]/10">
                    <MapPin className="h-6 w-6 text-[#1E1B4B]" />
                  </div>
                  <div>
                    <h3 className="font-bold mb-1">{t('contact.address')}</h3>
                    <p className="text-gray-600">{t('footer.address').replace('地址：', '').replace('Address: ', '')}</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="p-3 rounded-full bg-[#1E1B4B]/10">
                    <Clock className="h-6 w-6 text-[#1E1B4B]" />
                  </div>
                  <div>
                    <h3 className="font-bold mb-1">{t('contact.officeHours')}</h3>
                    <p className="text-gray-600">{t('contact.mondayToFriday')}</p>
                    <p className="text-gray-600">{t('contact.saturday')}</p>
                    <p className="text-gray-600">{t('contact.sundayHolidays')}</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Contact Form */}
        <div>
          <h2 className="text-2xl font-bold mb-8">{t('contact.contactForm')}</h2>

          <Card>
            <CardContent className="p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label htmlFor="Name" className="font-medium">
                      {t('contact.name')} <span className="text-red-500">{t('contact.required')}</span>
                    </label>
                    <Input
                      id="Name"
                      placeholder={t('contact.namePlaceholder')}
                      required
                      value={formData.Name}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="CompanyName" className="font-medium">
                      {t('contact.companyName')}
                    </label>
                    <Input
                      id="CompanyName"
                      placeholder={t('contact.companyPlaceholder')}
                      value={formData.CompanyName}
                      onChange={handleChange}
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label htmlFor="email" className="font-medium">
                      {t('contact.email')} <span className="text-red-500">{t('contact.required')}</span>
                    </label>
                    <Input
                      id="email"
                      type="email"
                      placeholder={t('contact.emailPlaceholder')}
                      required
                      value={formData.email}
                      onChange={handleChange}
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="Phone" className="font-medium">
                      {t('contact.phoneNumber')} <span className="text-red-500">{t('contact.required')}</span>
                    </label>
                    <Input
                      id="Phone"
                      placeholder={t('contact.phonePlaceholder')}
                      required
                      value={formData.Phone}
                      onChange={handleChange}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="Theme" className="font-medium">
                    {t('contact.subject')} <span className="text-red-500">{t('contact.required')}</span>
                  </label>
                  <Input
                    id="Theme"
                    placeholder={t('contact.subjectPlaceholder')}
                    required
                    value={formData.Theme}
                    onChange={handleChange}
                  />
                </div>

                <div className="space-y-2">
                  <label htmlFor="Message" className="font-medium">
                    {t('contact.message')} <span className="text-red-500">{t('contact.required')}</span>
                  </label>
                  <Textarea
                    id="Message"
                    placeholder={t('contact.messagePlaceholder')}
                    rows={6}
                    required
                    value={formData.Message}
                    onChange={handleChange}
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full bg-[#1E1B4B] hover:bg-[#1E1B4B]/90"
                  disabled={isLoading}
                >
                  {isLoading ? t('contact.submitting') : t('contact.submit')}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

// Component that uses searchParams - wrapped in Suspense
function ContactPageContent() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="contact.title"
        description="contact.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <Suspense fallback={<ContactLoading />}>
        <ContactContent />
      </Suspense>
    </div>
  )
}

// Main page component that wraps everything in Suspense
export default function ContactPage() {
  return (
    <Suspense fallback={<ContactLoading />}>
      <ContactPageContent />
    </Suspense>
  )
}