'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Button } from '@/components/ui/button'
import { Calendar, MapPin, Users, Building, ChevronDown, ChevronUp } from 'lucide-react'
import AnimatedHero from '@/components/animated-hero'
import { fetchExpoActivitiesData, ExpoActivity } from '@/lib/strapi'
import ImageSlider from '@/components/image-slider'

// Component for handling expandable text
function ExpandableText({ text, maxLength = 150 }: { text: string; maxLength?: number }) {
  const [isExpanded, setIsExpanded] = useState(false)

  if (text.length <= maxLength) {
    return <div className="text-muted-foreground">{text}</div>
  }

  return (
    <div className="text-muted-foreground">
      <div>
        {isExpanded ? text : `${text.substring(0, maxLength)}...`}
      </div>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
      >
        {isExpanded ? (
          <>
            <ChevronUp className="h-4 w-4 mr-1" />
            Read Less
          </>
        ) : (
          <>
            <ChevronDown className="h-4 w-4 mr-1" />
            Read More
          </>
        )}
      </Button>
    </div>
  )
}

export default function AffiliatedActivitiesPage() {
  const [activities, setActivities] = useState<ExpoActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadExpoActivities = async () => {
      try {
        setLoading(true)
        console.log('Fetching expo activities data...')
        const expoActivitiesData = await fetchExpoActivitiesData({
          populate: "*"
        })
        console.log('Fetched expo activities data:', expoActivitiesData)

        if (expoActivitiesData && expoActivitiesData.length > 0) {
          setActivities(expoActivitiesData)
        } else {
          console.log('No expo activities data found')
          setActivities([])
        }
      } catch (err) {
        console.error('Error fetching expo activities:', err)
        setError(err instanceof Error ? err.message : 'An error occurred while fetching expo activities data')
      } finally {
        setLoading(false)
      }
    }

    loadExpoActivities()
  }, [])

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
        <AnimatedHero
          title={{
            zh: "聯屬活動",
            en: "Affiliated Activities",
          }}
          description={{
            zh: "九龍總商會聯屬機構舉辦的各類展覽和活動",
            en: "Various exhibitions and activities organized by KCC affiliated organizations",
          }}
          image="/placeholder.svg"
        />
        <div className="container py-12">
          <div className="text-center">
            <p className="text-red-600 mb-4">載入聯屬活動時發生錯誤 / Error loading affiliated activities</p>
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <AnimatedHero
        title={{
          zh: "聯屬活動",
          en: "Affiliated Activities",
        }}
        description={{
          zh: "九龍總商會聯屬機構舉辦的各類展覽和活動，促進商業交流與合作",
          en: "Various exhibitions and activities organized by KCC affiliated organizations, promoting business exchange and cooperation",
        }}
        image="/placeholder.svg"
      />

      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <h2 className="text-center">聯屬活動 / Affiliated Activities</h2>
              <p>
                九龍總商會與多個聯屬機構合作，定期舉辦各類展覽會、博覽會和商業活動，
                為會員提供更多商業機會和交流平台，促進不同行業間的合作與發展。
              </p>
              <p>
                The KCC collaborates with various affiliated organizations to regularly organize exhibitions, expos and business activities,
                providing members with more business opportunities and exchange platforms, promoting cooperation and development between different industries.
              </p>
            </div>
          </CardContent>
        </Card>

        <section>
          {loading ? (
            <div className="grid md:grid-cols-2 gap-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {activities.length > 0 ? (
                <div className="space-y-6">
                  <div className="border-b pb-2">
                    <h2 className="text-2xl font-bold">
                      <span className="text-primary">展覽活動</span> / Exhibition Activities
                    </h2>
                    <p className="text-muted-foreground">聯屬機構舉辦的展覽和博覽會 / Exhibitions and expos organized by affiliated organizations</p>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    {activities.map((activity) => (
                      <Card key={activity.id} className="overflow-hidden hover:shadow-lg transition-all">
                        {/* Image Slider for multiple images */}
                        {activity.image && activity.image.length > 0 ? (
                          <ImageSlider
                            images={activity.image.map((img, index) => ({
                              id: img.id,
                              url: img.url,
                              formats: img.formats,
                              alt: img.alternativeText || `${activity.expo_name || "Expo Activity"} - Image ${index + 1}`
                            }))}
                            alt={`${activity.expo_name || "Expo Activity"} - Exhibition gallery`}
                            interval={3000}
                          />
                        ) : (
                          <div className="relative aspect-video">
                            <Image
                              src="/placeholder.svg"
                              alt={`${activity.expo_name || "Expo Activity"} - Exhibition image`}
                              fill
                              className="object-cover"
                            />
                          </div>
                        )}
                        <CardContent className="p-6">
                          {activity.date && (
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{new Date(activity.date).toLocaleDateString()}</time>
                            </div>
                          )}
                          <h3 className="text-xl font-bold mb-2">{activity.expo_name}</h3>
                          <div className="space-y-2 mb-4">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <MapPin className="h-4 w-4" />
                              <span>{activity.location}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <Building className="h-4 w-4" />
                              <span>Affiliated Exhibition</span>
                            </div>
                          </div>
                          <ExpandableText text={activity.description} maxLength={200} />
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    <span className="block">暫無聯屬活動資料</span>
                    <span className="block">No affiliated activities available at the moment</span>
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}
