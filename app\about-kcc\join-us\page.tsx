"use client"

import { useState, useEffect, Suspense, useMemo } from "react"
import { useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronUp } from "lucide-react"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"
import ImageSlider from "@/components/image-slider"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  url: string
  formats?: StrapiImageFormats
}

interface JoinUsData {
  id: number;
  documentId: string;
  title: string;
  requirements?: any[];
  benefits?: any[];
  applicationProcess?: any[];
  content?: any[];
  description?: any[];
  website?: string;
  order?: string;
  image?: StrapiImage | StrapiImage[]
  images?: StrapiImage[]
}

// Loading component
function JoinUsLoading() {
  return (
    <div className="min-h-screen">
      <div className="container py-12 space-y-8">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="text-center space-y-4">
              <div className="h-6 bg-muted rounded animate-pulse w-48 mx-auto" />
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-4 bg-muted rounded animate-pulse w-5/6 mx-auto" />
                <div className="h-4 bg-muted rounded animate-pulse w-4/6 mx-auto" />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-16">
          {Array(2).fill(null).map((_, index) => (
            <Card key={`loading-${index}`} className="overflow-hidden">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-10 items-start">
                  <div className="space-y-4">
                    <div className="h-8 bg-muted rounded animate-pulse w-3/4" />
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded animate-pulse" />
                      <div className="h-4 bg-muted rounded animate-pulse w-5/6" />
                      <div className="h-4 bg-muted rounded animate-pulse w-4/6" />
                    </div>
                  </div>
                  <div className="aspect-[16/12] bg-muted rounded animate-pulse" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

// Main content component
function JoinUsContent() {
  const searchParams = useSearchParams()
  const [joinUsData, setJoinUsData] = useState<JoinUsData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string>("");
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set())
  const [expandedApplicationProcess, setExpandedApplicationProcess] = useState<Set<number>>(new Set())

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  const toggleExpanded = (itemId: number) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }

  const toggleApplicationProcess = (itemId: number) => {
    setExpandedApplicationProcess(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api';
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae';

        // Build URL with locale parameter
        const url = new URL(`${strapiUrl}/join-uses`)
        url.searchParams.append('populate', '*')
        if (getStrapiLocale) {
          url.searchParams.append('locale', getStrapiLocale)
        }

        console.log('Join Us - Current language:', currentLang)
        console.log('Join Us - Strapi locale:', getStrapiLocale)
        console.log('Join Us - API URL:', url.toString())

        setDebugInfo("Fetching data from API...");

        const response = await fetch(url.toString(), {
          headers: {
            'Authorization': `Bearer ${strapiToken}`
          }
        });

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('API Response:', data);
        setDebugInfo(`API Response received. Looking for joinUsSection...`);

        // Based on the API response structure
        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
          console.log('Found join us data:', data.data);
          setDebugInfo(`Found ${data.data.length} join us items.`);
          setJoinUsData(data.data);
        } else {
          console.error('Join us data not found in response:', data);
          setDebugInfo(`Join us data not found in response: ${JSON.stringify(data).substring(0, 100)}...`);
          setError(t('joinUs.noData'));
        }
      } catch (error: any) {
        console.error('Error fetching join us data:', error);
        setDebugInfo(`Error: ${error.message}`);
        setError(t('joinUs.error'));
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [currentLang, getStrapiLocale]); // Re-run when language changes

  // Helper function to render rich text content
  const renderRichText = (content: any[]) => {
    if (!content || !Array.isArray(content)) {
      return <p className="text-muted-foreground">{t('joinUs.noContent')}</p>
    }

    return content.map((block, index) => {
      if (block.type === "paragraph") {
        return (
          <p key={index} className="mb-4">
            {block.children?.map((child: any, childIndex: number) => (
              <span key={childIndex}>{child.text}</span>
            ))}
          </p>
        )
      }
      return null
    })
  }

  // Function to render list items
  const renderList = (items: any[]) => {
    if (!items || !Array.isArray(items) || items.length === 0) {
      return <p className="text-muted-foreground">{t('joinUs.noContent')}</p>;
    }

    // Filter out empty items
    const validItems = items.filter(item => {
      if (item.type === "paragraph") {
        const text = item.children?.map((child: any) => child.text).join('').trim();
        return text && text.length > 0;
      }
      return item && item.toString().trim().length > 0;
    });

    if (validItems.length === 0) {
      return <p className="text-muted-foreground">{t('joinUs.noContent')}</p>;
    }

    return (
      <ul className="list-disc pl-5 space-y-2">
        {validItems.map((item, index) => {
          const content = item.type === "paragraph" ?
            item.children?.map((child: any) => child.text).join('') :
            item;

          return (
            <li key={index}>
              {content}
            </li>
          );
        })}
      </ul>
    );
  }

  // Helper function to get images for ImageSlider
  const getJoinUsImages = (item: JoinUsData): StrapiImage[] => {
    const images: StrapiImage[] = []

    // Check if image is an array (multiple images)
    if (Array.isArray(item.image)) {
      images.push(...item.image)
    } else if (item.image) {
      // Single image
      images.push(item.image)
    }

    // Also check images field
    if (item.images && Array.isArray(item.images)) {
      images.push(...item.images)
    }

    return images
  }

  return (
    <div className="container py-12">
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="prose prose-lg max-w-none text-center">
            <h2 className="text-center">{t('joinUs.staticTitle')}</h2>
            <p>{t('joinUs.staticDescription')}</p>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-16">
        {loading ? (
          // Loading skeletons
          Array(2).fill(null).map((_, index) => (
            <Card key={`skeleton-${index}`} className="overflow-hidden">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-10 items-start">
                  <div className="space-y-4">
                    <div className="h-8 bg-muted rounded animate-pulse w-3/4" />
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded animate-pulse" />
                      <div className="h-4 bg-muted rounded animate-pulse w-5/6" />
                      <div className="h-4 bg-muted rounded animate-pulse w-4/6" />
                    </div>
                  </div>
                  <div className="aspect-[16/12] bg-muted rounded animate-pulse" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : error ? (
          <Card>
            <CardContent className="p-8">
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                <p>{error}</p>
                <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>
              </div>
            </CardContent>
          </Card>
        ) : joinUsData.length > 0 ? (
          joinUsData.map((item) => {
            const isExpanded = expandedItems.has(item.id)
            const images = getJoinUsImages(item)

            // Determine if content is long enough to show read more/less
            const hasLongContent = (item.description && item.description.length > 2) ||
                                 (item.content && item.content.length > 2)

            return (
              <Card key={item.id} className="overflow-hidden">
                <CardContent className="p-8">
                  <div className="grid md:grid-cols-2 gap-8 items-start">
                    <div className="space-y-4">
                      <h1 className="text-3xl font-bold mb-4">{item.title}</h1>

                      {/* Description */}
                      {item.description && (
                        <div className={`space-y-4 ${!isExpanded && hasLongContent ? 'line-clamp-3' : ''}`}>
                          {renderRichText(item.description)}
                        </div>
                      )}

                      {/* Content */}
                      {item.content && (
                        <div className={`space-y-4 ${!isExpanded && hasLongContent ? 'line-clamp-3' : ''}`}>
                          {renderRichText(item.content)}
                        </div>
                      )}

                      {/* Website */}
                      {item.website && (
                        <p className="mt-4">
                          <strong>Website:</strong>{" "}
                          <a
                            href={
                              item.website.startsWith("http")
                                ? item.website
                                : `https://${item.website}`
                            }
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {item.website}
                          </a>
                        </p>
                      )}

                      {/* Requirements */}
                      {item.requirements && (
                        <div className="mt-6">
                          <h3 className="text-xl font-semibold mb-3">{t('joinUs.requirements')}</h3>
                          {renderList(item.requirements)}
                        </div>
                      )}

                      {/* Benefits */}
                      {item.benefits && (
                        <div className="mt-6">
                          <h3 className="text-xl font-semibold mb-3">{t('joinUs.benefits')}</h3>
                          {renderList(item.benefits)}
                        </div>
                      )}

                      {/* Read More/Less Button */}
                      {hasLongContent && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(item.id)}
                          className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                        >
                          {isExpanded ? (
                            <>
                              {t('joinUs.readLess')} <ChevronUp className="ml-1 h-3 w-3" />
                            </>
                          ) : (
                            <>
                              {t('joinUs.readMore')} <ChevronDown className="ml-1 h-3 w-3" />
                            </>
                          )}
                        </Button>
                      )}
                    </div>

                    {/* Image Section - Flexible Height */}
                    <div className="relative w-full min-h-[300px] max-h-[400px] shadow-md rounded-lg overflow-hidden">
                      {images.length > 0 ? (
                        images.length > 1 ? (
                          <div className="relative w-full h-[350px]">
                            <ImageSlider
                              images={images}
                              alt={item.title || "Join Us"}
                              interval={3000}
                            />
                          </div>
                        ) : (
                          <div className="relative w-full h-[350px]">
                            <Image
                              src={images[0].url}
                              alt={`${item.title} - Join Us`}
                              fill
                              className="object-cover"
                            />
                          </div>
                        )
                      ) : (
                        <div className="flex items-center justify-center h-[350px] bg-gray-100">
                          <p className="text-muted-foreground p-4 text-center">{t('joinUs.noImage')}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Application Process */}
                  {item.applicationProcess && item.applicationProcess.length > 0 &&
                   item.applicationProcess.some((step: any) => {
                     if (step.type === "paragraph") {
                       const text = step.children?.map((child: any) => child.text).join('').trim();
                       return text && text.length > 0;
                     }
                     return step && step.toString().trim().length > 0;
                   }) && (
                    <div className="mt-12">
                      <div className="flex items-center justify-between mb-6">
                        <h3 className="text-2xl font-semibold">{t('joinUs.applicationProcess')}</h3>
                        {(() => {
                          const validSteps = item.applicationProcess.filter((step: any) => {
                            if (step.type === "paragraph") {
                              const text = step.children?.map((child: any) => child.text).join('').trim();
                              return text && text.length > 0;
                            }
                            return step && step.toString().trim().length > 0;
                          });

                          return validSteps.length > 3 && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => toggleApplicationProcess(item.id)}
                              className="text-primary hover:text-primary/80"
                            >
                              {expandedApplicationProcess.has(item.id) ? (
                                <>
                                  {t('joinUs.readLess')} <ChevronUp className="ml-1 h-3 w-3" />
                                </>
                              ) : (
                                <>
                                  {t('joinUs.readMore')} <ChevronDown className="ml-1 h-3 w-3" />
                                </>
                              )}
                            </Button>
                          );
                        })()}
                      </div>

                      <div className="space-y-3">
                        {(() => {
                          const validSteps = item.applicationProcess.filter((step: any) => {
                            // Filter out empty steps
                            if (step.type === "paragraph") {
                              const text = step.children?.map((child: any) => child.text).join('').trim();
                              return text && text.length > 0;
                            }
                            return step && step.toString().trim().length > 0;
                          });

                          return validSteps
                            .slice(0, expandedApplicationProcess.has(item.id) ? validSteps.length : 3)
                            .map((step: any, stepIndex: number) => {
                              const content = step.type === "paragraph" ?
                                step.children?.map((child: any) => child.text).join('') :
                                step;

                              return (
                                <div key={stepIndex} className="flex items-start gap-3 p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500">
                                  <div className="flex-shrink-0">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                                  </div>
                                  <div className="flex-1">
                                    <p className="text-base leading-relaxed">
                                      {content}
                                    </p>
                                  </div>
                                </div>
                              );
                            });
                        })()}

                        {(() => {
                          const validSteps = item.applicationProcess.filter((step: any) => {
                            if (step.type === "paragraph") {
                              const text = step.children?.map((child: any) => child.text).join('').trim();
                              return text && text.length > 0;
                            }
                            return step && step.toString().trim().length > 0;
                          });

                          return !expandedApplicationProcess.has(item.id) && validSteps.length > 3 && (
                            <div className="text-center pt-2">
                              <p className="text-sm text-muted-foreground">
                                {t('joinUs.showingSteps')} (3 / {validSteps.length})
                              </p>
                            </div>
                          );
                        })()}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })
        ) : (
          <Card>
            <CardContent className="p-8">
              <div className="text-center py-12">
                <p className="text-muted-foreground">{t('joinUs.noData')}</p>
                <p className="mt-2 text-sm text-gray-500">{debugInfo}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

// Component that uses searchParams
function JoinUsPage() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="joinUs.title"
        description="joinUs.description"
        image="/placeholder.svg"
        lang={currentLang}
      />

      <Suspense fallback={<JoinUsLoading />}>
        <JoinUsContent />
      </Suspense>
    </div>
  )
}

// Main export with Suspense wrapper
export default function JoinUs() {
  return (
    <Suspense fallback={<JoinUsLoading />}>
      <JoinUsPage />
    </Suspense>
  )
}